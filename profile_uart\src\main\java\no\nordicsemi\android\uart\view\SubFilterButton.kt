/**
 * 子滤波开关按钮组件
 *
 * 提供特定信号类型的滤波开关控制：
 * 1. 视觉状态：
 *    - 启用状态：高亮显示，显示对应信号类型
 *    - 禁用状态：普通显示
 *    - 不可用状态：灰色显示，不可点击
 *
 * 2. 交互功能：
 *    - 仅在总开关启用时可点击
 *    - 点击切换对应信号的滤波状态
 *    - 提供触觉和视觉反馈
 *
 * 3. 设计特点：
 *    - 图标+文字组合
 *    - 符合Material Design规范
 *    - 状态变化动画
 *    - 层次化控制逻辑
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.GraphicEq
import androidx.compose.material.icons.filled.VolumeUp
import androidx.compose.material.icons.outlined.FavoriteBorder
import androidx.compose.material.icons.outlined.GraphicEq
import androidx.compose.material.icons.outlined.VolumeOff
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * 信号类型枚举
 */
enum class SignalType(val displayName: String) {
    ECG("心电"),
    EGG("肠电"),
    AUDIO("声音")
}

/**
 * 子滤波开关按钮
 *
 * @param signalType 信号类型
 * @param isEnabled 当前滤波状态，true表示启用滤波，false表示禁用
 * @param masterEnabled 总开关状态，false时此按钮不可点击
 * @param onClick 点击回调函数
 * @param modifier 修饰符
 */
@Composable
internal fun SubFilterButton(
    signalType: SignalType,
    isEnabled: Boolean,
    masterEnabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val isClickable = masterEnabled
    val backgroundColor = when {
        !masterEnabled -> MaterialTheme.colorScheme.surface
        isEnabled -> MaterialTheme.colorScheme.secondaryContainer
        else -> MaterialTheme.colorScheme.surfaceVariant
    }
    val contentColor = when {
        !masterEnabled -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
        isEnabled -> MaterialTheme.colorScheme.onSecondaryContainer
        else -> MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    IconButton(
        onClick = onClick,
        enabled = isClickable,
        modifier = modifier
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(8.dp)
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                imageVector = getSignalIcon(signalType, isEnabled && masterEnabled),
                contentDescription = "${signalType.displayName}滤波${if (isEnabled && masterEnabled) "启用" else "禁用"}",
                tint = contentColor
            )
            Text(
                text = signalType.displayName,
                style = MaterialTheme.typography.labelSmall,
                color = contentColor
            )
        }
    }
}

/**
 * 根据信号类型和状态获取对应的图标
 *
 * @param signalType 信号类型
 * @param isActive 是否激活状态
 * @return 对应的图标
 */
private fun getSignalIcon(signalType: SignalType, isActive: Boolean): ImageVector {
    return when (signalType) {
        SignalType.ECG -> if (isActive) Icons.Filled.Favorite else Icons.Outlined.FavoriteBorder
        SignalType.EGG -> if (isActive) Icons.Filled.GraphicEq else Icons.Outlined.GraphicEq
        SignalType.AUDIO -> if (isActive) Icons.Filled.VolumeUp else Icons.Outlined.VolumeOff
    }
}

/**
 * 预览组件 - 心电滤波启用状态
 */
@Preview(showBackground = true)
@Composable
private fun SubFilterButtonEcgEnabledPreview() {
    MaterialTheme {
        SubFilterButton(
            signalType = SignalType.ECG,
            isEnabled = true,
            masterEnabled = true,
            onClick = { }
        )
    }
}

/**
 * 预览组件 - 肠电滤波禁用状态
 */
@Preview(showBackground = true)
@Composable
private fun SubFilterButtonEggDisabledPreview() {
    MaterialTheme {
        SubFilterButton(
            signalType = SignalType.EGG,
            isEnabled = false,
            masterEnabled = true,
            onClick = { }
        )
    }
}

/**
 * 预览组件 - 声音滤波不可用状态
 */
@Preview(showBackground = true)
@Composable
private fun SubFilterButtonAudioUnavailablePreview() {
    MaterialTheme {
        SubFilterButton(
            signalType = SignalType.AUDIO,
            isEnabled = false,
            masterEnabled = false,
            onClick = { }
        )
    }
}
