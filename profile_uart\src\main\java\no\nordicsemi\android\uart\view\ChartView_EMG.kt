/**
 * 数据图表视图
 *
 * 实现了温湿度数据的图表显示界面：
 * 1. 主要组件：
 *    - 图表标题栏：包含缩放控制
 *    - 折线图：显示温湿度数据
 *    - 功能按钮区：
 *      * 保存数据到CSV
 *      * 断开连接
 *
 * 2. 交互功能：
 *    - 图表缩放控制
 *    - 数据导出功能
 *    - 设备断开连接
 *
 * 3. 布局特点：
 *    - 垂直布局结构
 *    - 合理的间距
 *    - 响应式设计
 *
 * 4. 数据处理：
 *    - 支持实时数据更新
 *    - CSV格式数据导出
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.data.FileStorageHelper
import no.nordicsemi.android.uart.data.UARTServiceData
import no.nordicsemi.android.ui.view.KeyValueField
import no.nordicsemi.android.ui.view.ScreenSection
import no.nordicsemi.android.ui.view.SectionTitle

/**
 * 图表视图组件
 *
 * @param state UART服务数据状态，包含温度、湿度数据
 * @param onEvent 事件处理回调函数，用于处理缩放等事件
 *
 * 功能说明：
 * 1. 界面结构：
 *    - 标题栏：显示图表标题和缩放控制按钮
 *    - 图表区域：显示温湿度数据折线图
 *
 * 2. 布局设计：
 *    - 使用Column布局实现垂直排列
 *    - 通过Spacer添加组件间距
 *    - 居中对齐所有组件
 *
 * 3. 交互处理：
 *    - 缩放控制：通过Menu组件实现
 *    - 数据展示：通过LineChartView实现
 */
@Composable
internal fun ChartView_EMG(viewState: UARTViewState, onEvent: (UARTViewEvent) -> Unit) {
    val state = viewState.uartManagerState  // 获取UARTServiceData

    // 创建垂直布局的列,并居中对齐其中的内容
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 创建一个屏幕区域
        ScreenSection {
            // 使用Row布局将两个组件放在同一行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically
            ) {

                // 添加5dp的间距
                Spacer(modifier = Modifier.width(20.dp))

                // 标题部分占据左侧五分之三
                Row(modifier = Modifier.weight(0.7f)) {
                    SectionTitle(
                        resId = R.drawable.ic_chart_line,
                        title = stringResource(id = R.string.uart_section_EMG),
                        menu = { Menu(state.zoomIn, onEvent) }
                    )
                }

                // 添加10dp的间距
                Spacer(modifier = Modifier.width(20.dp))

//                // EMG数据显示占据剩余部分
//                Row(modifier = Modifier.weight(0.3f)) {
//                    KeyValueField(
//                        key = stringResource(id = R.string.uart_section_Data_per_second),
//                        value = "75%"
//                    )
//                }
//                // 添加5dp的间距
//                Spacer(modifier = Modifier.width(5.dp))
            }

            // 显示数据图表
            LineChartView_EMG(
                dataPoints = if (viewState.filterState.isEcgFilterActive) {
                    state.filteredEmgMessages
                } else {
                    state.emgMessages
                },
                label = "EMG",
                color = android.graphics.Color.GREEN,
                zoomIn = state.zoomIn,
                isFiltered = viewState.filterState.isEcgFilterActive,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

/**
 * 菜单组件
 *
 * @param zoomIn 当前缩放状态
 * @param onEvent 事件处理回调函数，用于处理缩放切换事件
 *
 * 功能说明：
 * 1. 根据缩放状态显示不同图标：
 *    - 放大状态：显示缩小图标
 *    - 缩小状态：显示放大图标
 *
 * 2. 交互处理：
 *    - 点击时触发SwitchZoomEvent事件
 *    - 切换图表的缩放状态
 */
@Composable
private fun Menu(zoomIn: Boolean, onEvent: (UARTViewEvent) -> Unit) {
    val icon = when (zoomIn) {
        true -> R.drawable.ic_zoom_out
        false -> R.drawable.ic_zoom_in
    }
    IconButton(onClick = { onEvent(SwitchZoomEvent) }) {
        Icon(
            painter = painterResource(id = icon),
            contentDescription = stringResource(id = R.string.uart_zoom_icon)
        )
    }
}

/**
 * 预览组件
 *
 * 功能说明：
 * 1. 提供ChartView的预览功能
 * 2. 使用默认的UARTServiceData状态
 * 3. 用于开发时的UI预览和调试
 */
@Preview
@Composable
private fun PreviewChartView() {
    ChartView_EMG(viewState = UARTViewState()) { }
}
