package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import no.nordicsemi.android.uart.R

// 导入事件类型
import no.nordicsemi.android.uart.view.UARTViewEvent
import no.nordicsemi.android.uart.view.ToggleMasterFilterEvent
import no.nordicsemi.android.uart.view.ToggleEcgFilterEvent
import no.nordicsemi.android.uart.view.ToggleEggFilterEvent
import no.nordicsemi.android.uart.view.ToggleAudioFilterEvent
import no.nordicsemi.android.uart.view.DisconnectEvent

// 导入组件类型
import no.nordicsemi.android.uart.view.FilterState
import no.nordicsemi.android.uart.view.SignalType
import no.nordicsemi.android.uart.view.MasterFilterButton
import no.nordicsemi.android.uart.view.SubFilterButton

/**
 * 按钮组件
 *
 * 实现了层次化滤波控制和断开连接功能：
 * 1. 滤波控制行：
 *    - 总滤波开关（最左边）
 *    - 心电滤波开关
 *    - 肠电滤波开关
 *    - 声音滤波开关
 *
 * 2. 断开连接行：
 *    - 断开连接按钮（单独一行）
 *
 * 3. 布局特点：
 *    - 垂直排列（两行）
 *    - 居中对齐
 *    - 层次化控制逻辑
 */
@Composable
internal fun ButtonSection(
    filterState: FilterState,  // 滤波状态参数
    onEvent: (UARTViewEvent) -> Unit
) {

    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 滤波控制行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 总滤波开关
            MasterFilterButton(
                isEnabled = filterState.masterFilterEnabled,
                onClick = { onEvent(ToggleMasterFilterEvent) }
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 心电滤波开关
            SubFilterButton(
                signalType = SignalType.ECG,
                isEnabled = filterState.ecgFilterEnabled,
                masterEnabled = filterState.masterFilterEnabled,
                onClick = { onEvent(ToggleEcgFilterEvent) }
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 肠电滤波开关
            SubFilterButton(
                signalType = SignalType.EGG,
                isEnabled = filterState.eggFilterEnabled,
                masterEnabled = filterState.masterFilterEnabled,
                onClick = { onEvent(ToggleEggFilterEvent) }
            )

            Spacer(modifier = Modifier.width(8.dp))

            // 声音滤波开关
            SubFilterButton(
                signalType = SignalType.AUDIO,
                isEnabled = filterState.audioFilterEnabled,
                masterEnabled = filterState.masterFilterEnabled,
                onClick = { onEvent(ToggleAudioFilterEvent) }
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        // 断开连接行
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center
        ) {
            Button(
                onClick = { onEvent(DisconnectEvent) }
            ) {
                Text(text = stringResource(id = R.string.disconnect))
            }
        }
    }
}