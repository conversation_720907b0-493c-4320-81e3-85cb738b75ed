/**
 * UART界面状态管理
 *
 * 定义了UART界面的所有状态数据：
 * 1. 连接状态：
 *    - 设备连接状态
 *    - 连接错误信息
 *    - 重连状态
 *
 * 2. 数据状态：
 *    - 通信记录
 *    - 温湿度数据
 *    - 电池电量
 *
 * 3. 配置状态：
 *    - 当前配置
 *    - 宏命令列表
 *    - 编辑状态
 *
 * 4. 界面状态：
 *    - 显示模式
 *    - 缩放状态
 *    - 错误提示
 */

package no.nordicsemi.android.uart.view

import no.nordicsemi.android.uart.data.UARTConfiguration
import no.nordicsemi.android.uart.data.UARTMacro
import no.nordicsemi.android.uart.data.UARTServiceData

/**
 * 滤波器状态数据类
 *
 * 管理层次化的滤波器控制状态：
 * - masterFilterEnabled: 总滤波开关，控制是否启用滤波功能
 * - ecgFilterEnabled: 心电滤波开关，仅在总开关启用时有效
 * - eggFilterEnabled: 肠电滤波开关，仅在总开关启用时有效
 * - audioFilterEnabled: 声音滤波开关，仅在总开关启用时有效
 */
internal data class FilterState(
    val masterFilterEnabled: Boolean = false,    // 总滤波开关
    val ecgFilterEnabled: Boolean = false,       // 心电滤波开关
    val eggFilterEnabled: Boolean = false,       // 肠电滤波开关
    val audioFilterEnabled: Boolean = false      // 声音滤波开关
) {
    /**
     * 检查心电滤波是否实际生效
     */
    val isEcgFilterActive: Boolean
        get() = masterFilterEnabled && ecgFilterEnabled

    /**
     * 检查肠电滤波是否实际生效
     */
    val isEggFilterActive: Boolean
        get() = masterFilterEnabled && eggFilterEnabled

    /**
     * 检查声音滤波是否实际生效
     */
    val isAudioFilterActive: Boolean
        get() = masterFilterEnabled && audioFilterEnabled

    /**
     * 检查是否有任何滤波器生效
     */
    val hasAnyFilterActive: Boolean
        get() = masterFilterEnabled && (ecgFilterEnabled || eggFilterEnabled || audioFilterEnabled)
}

internal data class UARTViewState(
    val editedPosition: Int? = null,
    val selectedConfigurationName: String? = null,
    val isConfigurationEdited: Boolean = false,
    val configurations: List<UARTConfiguration> = emptyList(),
    val uartManagerState: UARTServiceData = UARTServiceData(),
    val isInputVisible: Boolean = true,
    val latestBodyValue: Int? = null,
    val latestAmbientValue: Int? = null,
    val latestEmgValue: Int? = null,
    val latestFilteredEmgValue: Int? = null,
    val isDataProcessing: Boolean = false,
    val filterState: FilterState = FilterState(),  // 新的滤波状态管理
    @Deprecated("使用filterState.isEcgFilterActive替代")
    val showFilteredData: Boolean = true  // 保持向后兼容，默认显示滤波数据
) {
    val showEditDialog: Boolean = editedPosition != null

    val selectedConfiguration: UARTConfiguration? = configurations.find { selectedConfigurationName == it.name }

    val selectedMacro: UARTMacro? = selectedConfiguration?.let { configuration ->
        editedPosition?.let {
            configuration.macros.getOrNull(it)
        }
    }
}
