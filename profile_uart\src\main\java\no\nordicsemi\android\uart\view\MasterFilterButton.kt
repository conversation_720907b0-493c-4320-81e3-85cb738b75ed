/**
 * 总滤波开关按钮组件
 *
 * 提供总体滤波功能的开关控制：
 * 1. 视觉状态：
 *    - 启用状态：高亮显示，显示"总滤波"文字
 *    - 禁用状态：普通显示，显示"原始"文字
 *
 * 2. 交互功能：
 *    - 点击切换总滤波状态
 *    - 提供触觉和视觉反馈
 *    - 控制所有子滤波器的可用性
 *
 * 3. 设计特点：
 *    - 图标+文字组合
 *    - 符合Material Design规范
 *    - 状态变化动画
 *    - 层次化控制标识
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.FilterAlt
import androidx.compose.material.icons.outlined.FilterAltOff
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

/**
 * 总滤波开关按钮
 *
 * @param isEnabled 当前总滤波状态，true表示启用总滤波，false表示禁用
 * @param onClick 点击回调函数
 * @param modifier 修饰符
 */
@Composable
internal fun MasterFilterButton(
    isEnabled: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    IconButton(
        onClick = onClick,
        modifier = modifier
            .background(
                color = if (isEnabled) 
                    MaterialTheme.colorScheme.primaryContainer 
                else 
                    MaterialTheme.colorScheme.surfaceVariant,
                shape = RoundedCornerShape(8.dp)
            )
            .padding(8.dp)
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Icon(
                imageVector = getMasterFilterIcon(isEnabled),
                contentDescription = if (isEnabled) "总滤波启用" else "总滤波禁用",
                tint = if (isEnabled) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant
            )
            Text(
                text = if (isEnabled) "总滤波" else "原始",
                style = MaterialTheme.typography.labelSmall,
                color = if (isEnabled) 
                    MaterialTheme.colorScheme.primary 
                else 
                    MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

/**
 * 根据总滤波状态获取对应的图标
 *
 * @param isEnabled 总滤波是否启用
 * @return 对应的图标
 */
private fun getMasterFilterIcon(isEnabled: Boolean): ImageVector {
    return if (isEnabled) {
        Icons.Filled.FilterAlt  // 总滤波启用图标
    } else {
        Icons.Outlined.FilterAltOff  // 总滤波禁用图标
    }
}

/**
 * 预览组件 - 总滤波启用状态
 */
@Preview(showBackground = true)
@Composable
private fun MasterFilterButtonEnabledPreview() {
    MaterialTheme {
        MasterFilterButton(
            isEnabled = true,
            onClick = { }
        )
    }
}

/**
 * 预览组件 - 总滤波禁用状态
 */
@Preview(showBackground = true)
@Composable
private fun MasterFilterButtonDisabledPreview() {
    MaterialTheme {
        MasterFilterButton(
            isEnabled = false,
            onClick = { }
        )
    }
}
