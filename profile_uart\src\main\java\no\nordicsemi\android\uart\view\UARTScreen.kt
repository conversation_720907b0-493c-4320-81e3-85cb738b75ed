/**
 * UART主屏幕界面
 *
 * 使用Jetpack Compose实现的UART主界面：
 * 1. 界面结构：
 *    - 顶部应用栏：显示设备名称和连接状态
 *    - 主要内容区：根据连接状态显示不同视图
 *    - 分页视图：包含输入界面和宏命令界面
 *
 * 2. 状态管理：
 *    - 使用ViewModel管理UI状态
 *    - 响应连接状态变化
 *    - 处理用户事件
 *
 * 3. 主要功能：
 *    - 设备连接状态显示
 *    - 键盘输入界面
 *    - 宏命令配置界面
 *    - 导航和断开连接
 *
 * 4. 组件复用：
 *    - 使用PagerView实现标签页
 *    - 复用通用UI组件
 */

package no.nordicsemi.android.uart.view

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import no.nordicsemi.android.common.ui.view.PagerView
import no.nordicsemi.android.common.ui.view.PagerViewEntity
import no.nordicsemi.android.common.ui.view.PagerViewItem
import no.nordicsemi.android.kotlin.ble.core.data.GattConnectionState
import no.nordicsemi.android.kotlin.ble.ui.scanner.view.DeviceConnectingView
import no.nordicsemi.android.kotlin.ble.ui.scanner.view.DeviceDisconnectedView
import no.nordicsemi.android.uart.R
import no.nordicsemi.android.uart.viewmodel.UARTViewModel
import no.nordicsemi.android.ui.view.NavigateUpButton
import no.nordicsemi.android.ui.view.ProfileAppBar

// 定义顶部应用栏高度
private val APP_BAR_HEIGHT = 50.dp

/**
 * UART主屏幕组件
 *
 * 主要功能：
 * 1. 初始化ViewModel和状态
 * 2. 构建顶部应用栏
 * 3. 根据连接状态显示不同界面
 * 4. 处理导航和断开连接事件
 */
@Composable
fun UARTScreen() {
    val viewModel: UARTViewModel = hiltViewModel()
    val state = viewModel.state.collectAsState().value

    val navigateUp = { viewModel.onEvent(NavigateUp) }

    Scaffold(
        topBar = {
            Box(modifier = Modifier.height(APP_BAR_HEIGHT)) {
                ProfileAppBar(
                    deviceName = state.uartManagerState.deviceName,
                    connectionState = state.uartManagerState.connectionState,
                    navigateUp = navigateUp,
                    disconnect = { viewModel.onEvent(DisconnectEvent) },
                    openLogger = { viewModel.onEvent(OpenLogger) }
                )
            }
        }
    ) {
        Column(
            modifier = Modifier.padding(it)
        ) {
            when (state.uartManagerState.connectionState?.state) {
                null,
                GattConnectionState.STATE_CONNECTING -> PaddingBox { DeviceConnectingView { NavigateUpButton(navigateUp) } }
                GattConnectionState.STATE_DISCONNECTED,
                GattConnectionState.STATE_DISCONNECTING -> PaddingBox {
                    DeviceDisconnectedView(state.uartManagerState.disconnectStatus) { NavigateUpButton(navigateUp) }
                }
                GattConnectionState.STATE_CONNECTED -> SuccessScreen()
            }
        }
    }
}

/**
 * 内容填充容器
 *
 * 功能：
 * 1. 为内容添加统一的内边距
 * 2. 使用Box布局包装内容
 * 3. 提供一致的视觉间距
 *
 * @param content 要显示的内容组件
 */
@Composable
private fun PaddingBox(content: @Composable () -> Unit) {
    Box(modifier = Modifier.padding(16.dp)) {
        content()
    }
}

/**
 * 连接成功后的主界面
 *
 * 功能：
 * 1. 创建分页视图实体
 * 2. 配置输入和宏命令两个标签页
 * 3. 设置页面间距和滚动行为
 * 4. 使用PagerView显示内容
 */
@Composable
private fun SuccessScreen() {
    val input = stringResource(id = R.string.uart_input)
    val macros = stringResource(id = R.string.uart_macros)
    val viewEntity = remember {
        PagerViewEntity(
            listOf(
                PagerViewItem(input) { KeyboardView() },
                PagerViewItem(macros) { MacroView() }
            )
        )
    }
    PagerView(
        viewEntity = viewEntity,
        modifier = Modifier.fillMaxSize(),
        itemSpacing = 16.dp,
        coroutineScope = rememberCoroutineScope(),
        scrollable = false
    )
}

/**
 * 键盘输入视图
 *
 * 功能：
 * 1. 获取ViewModel实例
 * 2. 收集当前状态
 * 3. 创建UARTContentView
 * 4. 处理用户输入事件
 */
@Composable
private fun KeyboardView() {
    val viewModel: UARTViewModel = hiltViewModel()
    val state = viewModel.state.collectAsState().value
    UARTContentView(state) { viewModel.onEvent(it) }
}

/**
 * 宏命令视图
 *
 * 功能：
 * 1. 获取ViewModel实例
 * 2. 收集当前状态
 * 3. 创建MacroSection
 * 4. 处理宏命令相关事件
 */
@Composable
private fun MacroView() {
    val viewModel: UARTViewModel = hiltViewModel()
    val state = viewModel.state.collectAsState().value
    MacroSection(state) { viewModel.onEvent(it) }
}
